/**
 * 响应式样式管理器
 * 基于iPhone 14 Pro Max (430px) 的动态缩放系统
 * 统一管理应用的响应式样式和适配逻辑，保持布局结构不变
 */

import { getDeviceInfo, pxToRpx, CommonSizes } from './adaptiveUtils.js';

class ResponsiveManager {
  constructor() {
    this.deviceInfo = null;
    this.isInitialized = false;
    this.listeners = [];
    this.currentBreakpoint = 'md'; // 默认中等屏幕
    this.designWidth = 430; // 设计基准宽度
    this.scaleFactor = 1; // 当前缩放因子
  }

  /**
   * 初始化响应式管理器
   */
  async init() {
    if (this.isInitialized) return;

    try {
      this.deviceInfo = await getDeviceInfo();
      this.currentBreakpoint = this.getBreakpoint();
      this.scaleFactor = this.calculateScaleFactor();
      this.isInitialized = true;

      // 设置CSS变量
      this.setCSSVariables();

      // 通知所有监听器
      this.notifyListeners();

      console.log('ResponsiveManager initialized:', {
        deviceInfo: this.deviceInfo,
        breakpoint: this.currentBreakpoint,
        scaleFactor: this.scaleFactor
      });
    } catch (error) {
      console.error('Failed to initialize ResponsiveManager:', error);
    }
  }

  /**
   * 计算动态缩放因子
   * 基于iPhone 14 Pro Max (430px) 为标准
   */
  calculateScaleFactor() {
    if (!this.deviceInfo) return 1;

    const currentWidth = this.deviceInfo.windowWidth;
    const scaleFactor = currentWidth / this.designWidth;

    // 限制缩放范围，避免极端情况
    return Math.max(0.7, Math.min(1.5, scaleFactor));
  }

  /**
   * 设置CSS变量
   */
  setCSSVariables() {
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      root.style.setProperty('--scale-factor', this.scaleFactor);
      root.style.setProperty('--scale-factor-inverse', 1 / this.scaleFactor);
    }
  }

  /**
   * 获取当前设备的断点
   * 主要用于极端情况的微调，大部分适配依靠动态缩放
   */
  getBreakpoint() {
    if (!this.deviceInfo) return 'md';

    const width = this.deviceInfo.windowWidth;

    // 简化断点，主要依靠动态缩放
    if (width < 280) return 'xs';      // 极小屏幕
    if (width < 320) return 'sm';      // 小屏幕
    if (width < 430) return 'md';      // 标准屏幕 (iPhone 14 Pro Max基准)
    if (width < 768) return 'lg';      // 大屏幕
    return 'xl';                       // 超大屏幕
  }

  /**
   * 添加响应式变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
    
    // 如果已经初始化，立即调用回调
    if (this.isInitialized) {
      callback(this.deviceInfo, this.currentBreakpoint);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.deviceInfo, this.currentBreakpoint);
      } catch (error) {
        console.error('Error in responsive listener:', error);
      }
    });
  }

  /**
   * 获取响应式样式 - 使用动态缩放保持布局一致性
   */
  getResponsiveStyles(baseStyles) {
    if (!this.deviceInfo) return baseStyles;

    // 直接使用计算出的缩放因子，而不是断点
    return this.adjustStylesWithScale(baseStyles, this.scaleFactor);
  }

  /**
   * 统一的比例缩放样式调整方法
   * 保持布局结构不变，只调整尺寸比例
   */
  adjustStylesWithScale(styles, scale) {
    const scaledStyles = { ...styles };

    // 需要缩放的属性列表
    const scalableProps = [
      'fontSize', 'padding', 'margin', 'borderRadius', 'width', 'height',
      'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
      'marginTop', 'marginRight', 'marginBottom', 'marginLeft',
      'gap', 'rowGap', 'columnGap', 'lineHeight', 'letterSpacing'
    ];

    // 对所有可缩放属性进行比例调整
    scalableProps.forEach(prop => {
      if (scaledStyles[prop] !== undefined) {
        scaledStyles[prop] = this.scaleValue(scaledStyles[prop], scale);
      }
    });

    return scaledStyles;
  }

  /**
   * 缩放数值 - 支持多种单位类型
   */
  scaleValue(value, scale) {
    if (typeof value === 'number') {
      return Math.round(value * scale);
    }

    if (typeof value === 'string') {
      // 支持rpx、px、rem、em等单位
      const unitMatch = value.match(/^(\d+(?:\.\d+)?)(rpx|px|rem|em|%)$/);
      if (unitMatch) {
        const [, num, unit] = unitMatch;
        const scaledNum = Math.round(parseFloat(num) * scale);
        return `${scaledNum}${unit}`;
      }

      // 支持复合值，如 "10rpx 20rpx"
      if (value.includes('rpx') || value.includes('px')) {
        return value.replace(/(\d+(?:\.\d+)?)(rpx|px)/g, (match, num, unit) => {
          const scaledNum = Math.round(parseFloat(num) * scale);
          return `${scaledNum}${unit}`;
        });
      }
    }

    return value;
  }

  /**
   * 获取响应式字体大小
   */
  getResponsiveFontSize(baseFontSize) {
    return Math.round(baseFontSize * this.scaleFactor);
  }

  /**
   * 获取响应式间距
   */
  getResponsiveSpacing(baseSpacing) {
    return Math.round(baseSpacing * this.scaleFactor);
  }

  /**
   * 获取当前缩放因子
   */
  getScaleFactor() {
    return this.scaleFactor;
  }

  /**
   * 获取当前设备信息
   */
  getDeviceInfo() {
    return this.deviceInfo;
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    return this.currentBreakpoint;
  }

  /**
   * 判断是否为小屏设备
   */
  isSmallScreen() {
    return this.scaleFactor < 0.9;
  }

  /**
   * 判断是否为大屏设备
   */
  isLargeScreen() {
    return this.scaleFactor > 1.1;
  }

  /**
   * 获取网格列数 - 保持布局结构一致性
   * 不再根据屏幕大小改变列数，而是通过缩放保持布局
   */
  getGridColumns(defaultColumns = 2) {
    // 始终返回默认列数，保持布局结构不变
    // 通过CSS缩放来适配不同屏幕，而不是改变网格结构
    return defaultColumns;
  }

  /**
   * 更新缩放因子（当窗口大小改变时）
   */
  updateScaleFactor() {
    if (this.deviceInfo) {
      this.scaleFactor = this.calculateScaleFactor();
      this.setCSSVariables();
      this.notifyListeners();
    }
  }

  /**
   * 创建响应式CSS类名
   */
  createResponsiveClass(baseClass) {
    return `${baseClass} ${baseClass}--${this.currentBreakpoint}`;
  }
}

// 创建全局实例
const responsiveManager = new ResponsiveManager();

// 导出实例和工具函数
export default responsiveManager;

export {
  ResponsiveManager,
  responsiveManager,
  pxToRpx,
  CommonSizes
};

// 自动初始化（在应用启动时）
export const initResponsiveManager = async () => {
  await responsiveManager.init();
  return responsiveManager;
};
