/**
 * 响应式增强样式
 * 确保所有页面在不同手机宽度下能够适应
 * 动态调整卡片大小、比例、文字大小，保证相对比例不变
 */

/* ================== 全局响应式基础 ================== */
:root {
  /* 响应式断点 */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 375px;
  --breakpoint-md: 414px;
  --breakpoint-lg: 430px;
  --breakpoint-xl: 768px;
  
  /* 响应式缩放因子 */
  --scale-xs: 0.75;
  --scale-sm: 0.85;
  --scale-md: 0.95;
  --scale-lg: 1.0;
  --scale-xl: 1.1;
}

/* ================== 通用卡片响应式 ================== */
.responsive-card {
  width: calc(100% - 32px);
  max-width: 600px;
  margin: 12px auto;
  padding: clamp(16px, 4vw, 24px);
  border-radius: clamp(16px, 4vw, 24px);
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 统一所有页面的卡片样式 */
.radiation-monitoring-card-redesigned,
.device-summary,
.monitoring-section,
.radiation-matrix,
.charts-container-optimized,
.time-filter-container,
.modern-header,
.health-metrics-container,
.user-info-card,
.settings-sections {
  width: calc(100% - 32px) !important;
  max-width: 600px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  box-sizing: border-box !important;
}

/* ================== 响应式文字系统 ================== */
.responsive-text-xs { font-size: clamp(10px, 2.5vw, 12px); }
.responsive-text-sm { font-size: clamp(12px, 3vw, 14px); }
.responsive-text-base { font-size: clamp(14px, 3.5vw, 16px); }
.responsive-text-lg { font-size: clamp(16px, 4vw, 20px); }
.responsive-text-xl { font-size: clamp(20px, 5vw, 24px); }
.responsive-text-2xl { font-size: clamp(24px, 6vw, 32px); }
.responsive-text-3xl { font-size: clamp(28px, 7vw, 42px); }
.responsive-text-4xl { font-size: clamp(32px, 8vw, 48px); }

/* 防止文字换行的通用类 */
.no-wrap {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* ================== 响应式间距系统 ================== */
.responsive-gap-xs { gap: clamp(4px, 1vw, 8px); }
.responsive-gap-sm { gap: clamp(8px, 2vw, 12px); }
.responsive-gap-base { gap: clamp(12px, 3vw, 16px); }
.responsive-gap-lg { gap: clamp(16px, 4vw, 24px); }
.responsive-gap-xl { gap: clamp(20px, 5vw, 32px); }

.responsive-padding-xs { padding: clamp(8px, 2vw, 12px); }
.responsive-padding-sm { padding: clamp(12px, 3vw, 16px); }
.responsive-padding-base { padding: clamp(16px, 4vw, 20px); }
.responsive-padding-lg { padding: clamp(20px, 5vw, 28px); }
.responsive-padding-xl { padding: clamp(24px, 6vw, 32px); }

.responsive-margin-xs { margin: clamp(8px, 2vw, 12px); }
.responsive-margin-sm { margin: clamp(12px, 3vw, 16px); }
.responsive-margin-base { margin: clamp(16px, 4vw, 20px); }
.responsive-margin-lg { margin: clamp(20px, 5vw, 28px); }
.responsive-margin-xl { margin: clamp(24px, 6vw, 32px); }

/* ================== 响应式网格系统 ================== */
.responsive-grid-2x2 {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  grid-template-rows: 1fr 1fr !important;
  gap: clamp(12px, 3vw, 20px) !important;
  width: calc(100% - 32px) !important;
  max-width: 600px !important;
  margin: 0 auto !important;
  box-sizing: border-box !important;
}

.responsive-grid-4-cols {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: clamp(8px, 2vw, 16px) !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* ================== 响应式图标系统 ================== */
.responsive-icon-xs { width: clamp(12px, 3vw, 16px); height: clamp(12px, 3vw, 16px); }
.responsive-icon-sm { width: clamp(16px, 4vw, 20px); height: clamp(16px, 4vw, 20px); }
.responsive-icon-base { width: clamp(20px, 5vw, 24px); height: clamp(20px, 5vw, 24px); }
.responsive-icon-lg { width: clamp(24px, 6vw, 32px); height: clamp(24px, 6vw, 32px); }
.responsive-icon-xl { width: clamp(32px, 8vw, 40px); height: clamp(32px, 8vw, 40px); }

/* ================== 小屏幕特殊适配 ================== */
@media (max-width: 375px) {
  .responsive-card {
    width: calc(100% - 24px);
    margin: 8px auto;
    padding: clamp(12px, 3vw, 16px);
  }
  
  .responsive-grid-2x2 {
    width: calc(100% - 24px) !important;
    gap: clamp(8px, 2vw, 12px) !important;
  }
  
  .responsive-grid-4-cols {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: clamp(6px, 1.5vw, 10px) !important;
  }
}

/* ================== 中等屏幕适配 ================== */
@media (min-width: 376px) and (max-width: 414px) {
  .responsive-card {
    width: calc(100% - 28px);
    margin: 10px auto;
    padding: clamp(14px, 3.5vw, 20px);
  }
}

/* ================== 大屏幕适配 ================== */
@media (min-width: 768px) {
  .responsive-card {
    width: calc(100% - 64px);
    max-width: 700px;
    margin: 16px auto;
    padding: clamp(24px, 3vw, 32px);
  }
  
  .responsive-grid-4-cols {
    grid-template-columns: repeat(4, 1fr) !important;
    gap: clamp(16px, 2vw, 24px) !important;
  }
}

/* ================== 强制布局保持 ================== */
.force-layout-preserve {
  /* 强制保持布局结构，防止被其他样式覆盖 */
  display: grid !important;
  grid-template-columns: inherit !important;
  grid-template-rows: inherit !important;
  gap: inherit !important;
}

/* ================== 响应式高度系统 ================== */
.responsive-min-height-xs { min-height: clamp(60px, 15vw, 80px); }
.responsive-min-height-sm { min-height: clamp(80px, 20vw, 100px); }
.responsive-min-height-base { min-height: clamp(100px, 25vw, 120px); }
.responsive-min-height-lg { min-height: clamp(120px, 30vw, 160px); }
.responsive-min-height-xl { min-height: clamp(160px, 40vw, 200px); }

/* ================== 响应式边框圆角 ================== */
.responsive-rounded-xs { border-radius: clamp(4px, 1vw, 8px); }
.responsive-rounded-sm { border-radius: clamp(8px, 2vw, 12px); }
.responsive-rounded-base { border-radius: clamp(12px, 3vw, 16px); }
.responsive-rounded-lg { border-radius: clamp(16px, 4vw, 20px); }
.responsive-rounded-xl { border-radius: clamp(20px, 5vw, 24px); }

/* ================== 响应式阴影系统 ================== */
.responsive-shadow-sm { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04); }
.responsive-shadow-base { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06); }
.responsive-shadow-lg { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08); }
.responsive-shadow-xl { box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12); }

/* ================== 响应式过渡动画 ================== */
.responsive-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.responsive-hover-lift:hover {
  transform: translateY(clamp(-2px, -0.5vw, -4px));
  box-shadow: 0 clamp(8px, 2vw, 16px) clamp(24px, 6vw, 48px) rgba(0, 0, 0, 0.12);
}

/* ================== 响应式Flex布局 ================== */
.responsive-flex {
  display: flex;
  gap: clamp(8px, 2vw, 16px);
  align-items: center;
}

.responsive-flex-col {
  display: flex;
  flex-direction: column;
  gap: clamp(8px, 2vw, 16px);
}

.responsive-flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: clamp(8px, 2vw, 16px);
}

/* ================== 响应式内容保护 ================== */
.responsive-content-protect {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  min-width: 0;
}

.responsive-content-wrap {
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
}
