/**
 * 响应式设计系统 - 基于iPhone 14 Pro Max (430px) 设计稿
 * 使用动态缩放实现完美适配，保持布局结构不变
 * 设计稿基准: 430px -> 750rpx
 */

/* ================== 动态缩放核心系统 ================== */
/*
 * 核心理念：基于430px设计稿，使用CSS calc()和视口单位实现连续缩放
 * 转换公式: rpx = (px / 430) * 750
 * 动态缩放: scale = clamp(0.7, 100vw / 430px, 1.5)
 */

/* ================== 动态缩放核心变量 ================== */
:root {
  /* 设计基准 */
  --design-width: 430px;
  --design-height: 932px;

  /* 动态缩放因子 - 基于视口宽度的连续缩放，限制缩放范围保持可读性 */
  --scale-factor: clamp(0.85, calc(100vw / var(--design-width)), 1.2);
  --scale-factor-inverse: calc(1 / var(--scale-factor));

  /* 主题色彩系统 */
  --primary-color: #00b4d8;
  --primary-light: #90e0ef;
  --primary-dark: #0077b6;
  --primary-gradient: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);

  /* 辅助色系 */
  --secondary-color: #8b5cf6;
  --accent-color: #06ffa5;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --success-color: #10b981;

  /* 中性色系 - 优雅灰色调 */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* 背景色系 */
  --bg-primary: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  --bg-secondary: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  --bg-card: rgba(255, 255, 255, 0.9);
  --bg-card-hover: rgba(255, 255, 255, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.85);

  /* 文字颜色 */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;

  /* ================== 动态响应式尺寸系统 ================== */
  /* 基于430px设计稿的动态缩放转换 */

  /* 字体大小 - 动态缩放 (基于430px设计稿)，保持最小可读性 */
  --font-xs: clamp(14rpx, calc(17.44rpx * var(--scale-factor)), 21rpx);    /* 10px -> (10/430)*750 */
  --font-sm: clamp(17rpx, calc(20.93rpx * var(--scale-factor)), 25rpx);    /* 12px -> (12/430)*750 */
  --font-base: clamp(20rpx, calc(24.42rpx * var(--scale-factor)), 29rpx);  /* 14px -> (14/430)*750 */
  --font-lg: clamp(23rpx, calc(27.91rpx * var(--scale-factor)), 33rpx);    /* 16px -> (16/430)*750 */
  --font-xl: clamp(26rpx, calc(31.40rpx * var(--scale-factor)), 38rpx);    /* 18px -> (18/430)*750 */
  --font-2xl: clamp(32rpx, calc(38.37rpx * var(--scale-factor)), 46rpx);   /* 22px -> (22/430)*750 */
  --font-3xl: clamp(38rpx, calc(45.35rpx * var(--scale-factor)), 54rpx);   /* 26px -> (26/430)*750 */
  --font-4xl: clamp(47rpx, calc(55.81rpx * var(--scale-factor)), 67rpx);   /* 32px -> (32/430)*750 */

  /* 圆角系统 - 动态缩放 */
  --radius-xs: calc(3.49rpx * var(--scale-factor));   /* 2px -> (2/430)*750 */
  --radius-sm: calc(6.98rpx * var(--scale-factor));   /* 4px -> (4/430)*750 */
  --radius-base: calc(10.47rpx * var(--scale-factor)); /* 6px -> (6/430)*750 */
  --radius-lg: calc(13.95rpx * var(--scale-factor));  /* 8px -> (8/430)*750 */
  --radius-xl: calc(20.93rpx * var(--scale-factor));  /* 12px -> (12/430)*750 */
  --radius-2xl: calc(27.91rpx * var(--scale-factor)); /* 16px -> (16/430)*750 */
  --radius-3xl: calc(34.88rpx * var(--scale-factor)); /* 20px -> (20/430)*750 */
  --radius-full: 9999rpx;

  /* 间距系统 - 动态缩放，保持最小间距 */
  --space-xs: clamp(5rpx, calc(6.98rpx * var(--scale-factor)), 8rpx);    /* 4px -> (4/430)*750 */
  --space-sm: clamp(10rpx, calc(13.95rpx * var(--scale-factor)), 17rpx);   /* 8px -> (8/430)*750 */
  --space-base: clamp(16rpx, calc(20.93rpx * var(--scale-factor)), 25rpx); /* 12px -> (12/430)*750 */
  --space-lg: clamp(22rpx, calc(27.91rpx * var(--scale-factor)), 33rpx);   /* 16px -> (16/430)*750 */
  --space-xl: clamp(28rpx, calc(34.88rpx * var(--scale-factor)), 42rpx);   /* 20px -> (20/430)*750 */
  --space-2xl: clamp(34rpx, calc(41.86rpx * var(--scale-factor)), 50rpx);  /* 24px -> (24/430)*750 */
  --space-3xl: clamp(45rpx, calc(55.81rpx * var(--scale-factor)), 67rpx);  /* 32px -> (32/430)*750 */
  --space-4xl: clamp(56rpx, calc(69.77rpx * var(--scale-factor)), 84rpx);  /* 40px -> (40/430)*750 */

  /* 阴影系统 - 动态缩放 */
  --shadow-sm: 0 calc(3.49rpx * var(--scale-factor)) calc(6.98rpx * var(--scale-factor)) rgba(0, 0, 0, 0.06);    /* 0 2px 4px */
  --shadow-base: 0 calc(6.98rpx * var(--scale-factor)) calc(10.47rpx * var(--scale-factor)) rgba(0, 0, 0, 0.07); /* 0 4px 6px */
  --shadow-lg: 0 calc(17.44rpx * var(--scale-factor)) calc(26.16rpx * var(--scale-factor)) rgba(0, 0, 0, 0.1);   /* 0 10px 15px */
  --shadow-xl: 0 calc(34.88rpx * var(--scale-factor)) calc(43.60rpx * var(--scale-factor)) rgba(0, 0, 0, 0.1);   /* 0 20px 25px */
  --shadow-2xl: 0 calc(43.60rpx * var(--scale-factor)) calc(87.21rpx * var(--scale-factor)) rgba(0, 0, 0, 0.25); /* 0 25px 50px */

  /* 组件尺寸 - 动态缩放 */
  --button-height: calc(76.74rpx * var(--scale-factor));    /* 44px -> (44/430)*750 */
  --input-height: calc(69.77rpx * var(--scale-factor));     /* 40px -> (40/430)*750 */
  --card-padding: calc(27.91rpx * var(--scale-factor));     /* 16px -> (16/430)*750 */
  --section-spacing: calc(41.86rpx * var(--scale-factor));  /* 24px -> (24/430)*750 */

  /* 图标尺寸 - 动态缩放 */
  --icon-xs: calc(20.93rpx * var(--scale-factor));    /* 12px -> (12/430)*750 */
  --icon-sm: calc(27.91rpx * var(--scale-factor));    /* 16px -> (16/430)*750 */
  --icon-base: calc(34.88rpx * var(--scale-factor));  /* 20px -> (20/430)*750 */
  --icon-lg: calc(41.86rpx * var(--scale-factor));    /* 24px -> (24/430)*750 */
  --icon-xl: calc(55.81rpx * var(--scale-factor));    /* 32px -> (32/430)*750 */
  --icon-2xl: calc(69.77rpx * var(--scale-factor));   /* 40px -> (40/430)*750 */

  /* ================== 状态色系 ================== */

  /* 辐射监测状态色 */
  --radiation-safe: #10b981;
  --radiation-safe-bg: rgba(16, 185, 129, 0.1);
  --radiation-safe-border: rgba(16, 185, 129, 0.2);

  --radiation-warning: #f59e0b;
  --radiation-warning-bg: rgba(245, 158, 11, 0.1);
  --radiation-warning-border: rgba(245, 158, 11, 0.2);

  --radiation-danger: #ef4444;
  --radiation-danger-bg: rgba(239, 68, 68, 0.1);
  --radiation-danger-border: rgba(239, 68, 68, 0.2);

  /* ================== 组件变量 ================== */

  /* 卡片 */
  --card-bg: var(--bg-card);
  --card-border: rgba(226, 232, 240, 0.8);
  --card-shadow: var(--shadow-lg);
  --card-radius: var(--radius-xl);

  /* 按钮 */
  --btn-radius: var(--radius-lg);
  --btn-shadow: var(--shadow-base);

  /* 输入框 */
  --input-bg: var(--white);
  --input-border: var(--gray-300);
  --input-border-focus: var(--primary-color);
  --input-radius: var(--radius-base);

  /* ================== 动画变量 ================== */
  --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* ================== 兼容性变量 ================== */

  /* 兼容原有uni-app变量 */
  --uni-color-primary: var(--primary-color);
  --uni-color-success: var(--success-color);
  --uni-color-warning: var(--warning-color);
  --uni-color-error: var(--danger-color);

  --uni-text-color: var(--text-primary);
  --uni-text-color-inverse: var(--text-inverse);
  --uni-text-color-grey: var(--text-muted);
  --uni-text-color-placeholder: var(--gray-400);
  --uni-text-color-disable: var(--gray-300);

  --uni-bg-color: var(--white);
  --uni-bg-color-grey: var(--gray-100);
  --uni-bg-color-hover: var(--gray-50);
  --uni-bg-color-mask: rgba(15, 23, 42, 0.5);

  --uni-border-color: var(--gray-300);

  --uni-font-size-sm: var(--font-sm);
  --uni-font-size-base: var(--font-base);
  --uni-font-size-lg: var(--font-lg);

  --uni-border-radius-sm: var(--radius-sm);
  --uni-border-radius-base: var(--radius-base);
  --uni-border-radius-lg: var(--radius-lg);
  --uni-border-radius-circle: 50%;

  --uni-spacing-row-sm: var(--space-xs);
  --uni-spacing-row-base: var(--space-base);
  --uni-spacing-row-lg: var(--space-lg);

  --uni-spacing-col-sm: var(--space-xs);
  --uni-spacing-col-base: var(--space-base);
  --uni-spacing-col-lg: var(--space-lg);

  --uni-opacity-disabled: 0.5;

  /* 文章场景相关 */
  --uni-color-title: var(--text-primary);
  --uni-font-size-title: var(--font-xl);
  --uni-color-subtitle: var(--text-secondary);
  --uni-font-size-subtitle: var(--font-lg);
  --uni-color-paragraph: var(--text-secondary);
  --uni-font-size-paragraph: var(--font-base);

  /* ================== 流体间距系统 ================== */
  /* 用于网格和布局的流体间距，确保布局结构保持一致 */
  --space-base-fluid: calc(var(--space-base) * var(--scale-factor));
  --space-lg-fluid: calc(var(--space-lg) * var(--scale-factor));
  --space-xl-fluid: calc(var(--space-xl) * var(--scale-factor));

  /* ================== 响应式断点 ================== */
  /* 基于iPhone 14 Pro Max (430px) 为标准的断点设置 */
  /* 注意：这些断点主要用于极端情况的微调，主要依靠动态缩放 */
  --breakpoint-xs: 280px;   /* 极小屏手机 */
  --breakpoint-sm: 320px;   /* 小屏手机 */
  --breakpoint-md: 430px;   /* 设计基准 iPhone 14 Pro Max */
  --breakpoint-lg: 480px;   /* 大屏手机 */
  --breakpoint-xl: 768px;   /* 平板 */
}

/* ================== 响应式媒体查询 ================== */
/* 主要依靠动态缩放，媒体查询仅用于极端情况的微调 */

/* 极小屏设备微调 (width < 280px) */
@media (max-width: 280px) {
  :root {
    /* 在极小屏幕上稍微增加缩放下限，确保可读性 */
    --scale-factor: clamp(0.75, calc(100vw / var(--design-width)), 1.5);
  }
}

/* 超大屏设备微调 (width > 768px) */
@media (min-width: 768px) {
  :root {
    /* 在大屏幕上限制最大缩放，避免元素过大 */
    --scale-factor: clamp(0.7, calc(100vw / var(--design-width)), 1.3);
  }
}

/* ================== 布局保持系统 ================== */
/* 确保所有布局结构在不同屏幕尺寸下保持一致 */

/* 强制保持网格布局结构 */
.responsive-grid,
.info-cards-section,
.stats-cards,
.time-weather-section,
.data-grid,
.quick-overview,
.secondary-data-grid,
.bottom-data-grid,
.info-grid-redesigned,
.actions-grid {
  /* 保持原有的网格列数，不因屏幕大小改变 */
  gap: var(--space-base-fluid) !important;
}

/* 确保2x2网格始终保持2x2结构 */
.grid-2x2,
.info-cards-section,
.bottom-data-grid {
  grid-template-columns: repeat(2, 1fr) !important;
}

/* 确保1列布局保持1列 */
.grid-1-col {
  grid-template-columns: 1fr !important;
}

/* 确保3列布局保持3列 */
.grid-3-col {
  grid-template-columns: repeat(3, 1fr) !important;
}

/* 确保4列布局保持4列 */
.grid-4-col {
  grid-template-columns: repeat(4, 1fr) !important;
}

/* ================== 响应式布局修复 ================== */

/* 防止数字换行 - 确保数字在一行显示 */
.number-display,
.value-display,
.metric-value,
.data-value {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-width: 0 !important;
}

/* 卡片最小尺寸保护 - 防止卡片过小 */
.card,
.info-card,
.data-card,
.metric-card {
  min-width: 120rpx !important;
  min-height: 80rpx !important;
}

/* 图表容器保护 - 确保图表有足够空间 */
.chart-container,
.echarts-container {
  min-width: 200rpx !important;
  min-height: 150rpx !important;
  padding: var(--space-sm) !important;
}

/* 防止元素重叠 - 增加z-index层级管理 */
.overlay-content {
  z-index: 10 !important;
}

.chart-overlay {
  z-index: 5 !important;
}

/* 确保图表坐标轴可见 */
.chart-axis,
.echarts-axis {
  font-size: var(--font-xs) !important;
  color: var(--text-secondary) !important;
}

/* 卡片对齐修复 */
.card-grid,
.info-grid {
  align-items: stretch !important;
  justify-items: stretch !important;
}

.card-grid > *,
.info-grid > * {
  width: 100% !important;
  height: 100% !important;
}

/* 响应式文字大小保护 */
@media (max-width: 320px) {
  .number-display,
  .value-display {
    font-size: var(--font-sm) !important;
  }

  .card-title {
    font-size: var(--font-xs) !important;
  }
}

/* 小屏幕特殊处理 */
@media (max-width: 280px) {
  .grid-2x2 {
    gap: var(--space-xs) !important;
  }

  .card {
    padding: var(--space-xs) !important;
  }
}

/* ================== 通用响应式修复 ================== */

/* 防止所有文本和数字元素换行 */
.text-nowrap,
.number-text,
.value-text,
.stat-text,
.metric-text {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 确保所有网格保持结构 */
.grid-container,
.grid-layout,
.card-grid,
.data-grid {
  display: grid !important;
  grid-template-columns: var(--grid-columns, repeat(2, 1fr)) !important;
  gap: clamp(8rpx, 2vw, 24rpx) !important;
}

/* 卡片内容保护 */
.card-content,
.card-body {
  min-height: 60rpx !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
}

/* 图表和图形元素保护 */
.chart,
.graph,
.visualization {
  min-width: 200rpx !important;
  min-height: 120rpx !important;
  overflow: hidden !important;
}

/* 按钮和交互元素保护 */
.btn,
.button,
.interactive {
  min-height: 44rpx !important; /* 符合触摸标准 */
  min-width: 44rpx !important;
}