/**
 * 动态缩放CSS系统
 * 基于iPhone 14 Pro Max (430px) 的动态缩放样式
 * 与uni.scss的动态缩放系统保持一致
 */

/* ================== 动态缩放工具类 ================== */
/* 这些类使用uni.scss中定义的动态缩放变量 */

/* 动态缩放容器 */
.dynamic-scale-container {
  width: 100%;
  height: 100%;
  transform-origin: top left;
  /* 使用uni.scss中的缩放因子 */
  transform: scale(var(--scale-factor));
}

/* 反向缩放容器（用于需要保持原始大小的元素） */
.dynamic-scale-inverse {
  transform: scale(var(--scale-factor-inverse));
  transform-origin: center;
}

/* ================== 动态字体类 ================== */
/* 使用uni.scss中定义的动态字体变量 */
.text-xs-scaled { font-size: var(--font-xs); }
.text-sm-scaled { font-size: var(--font-sm); }
.text-base-scaled { font-size: var(--font-base); }
.text-lg-scaled { font-size: var(--font-lg); }
.text-xl-scaled { font-size: var(--font-xl); }
.text-2xl-scaled { font-size: var(--font-2xl); }
.text-3xl-scaled { font-size: var(--font-3xl); }

/* ================== 动态间距类 ================== */
/* 使用uni.scss中定义的动态间距变量 */

/* Padding */
.p-xs-scaled { padding: var(--space-xs); }
.p-sm-scaled { padding: var(--space-sm); }
.p-base-scaled { padding: var(--space-base); }
.p-lg-scaled { padding: var(--space-lg); }
.p-xl-scaled { padding: var(--space-xl); }
.p-2xl-scaled { padding: var(--space-2xl); }

/* Margin */
.m-xs-scaled { margin: var(--space-xs); }
.m-sm-scaled { margin: var(--space-sm); }
.m-base-scaled { margin: var(--space-base); }
.m-lg-scaled { margin: var(--space-lg); }
.m-xl-scaled { margin: var(--space-xl); }
.m-2xl-scaled { margin: var(--space-2xl); }

/* Gap */
.gap-xs-scaled { gap: var(--space-xs); }
.gap-sm-scaled { gap: var(--space-sm); }
.gap-base-scaled { gap: var(--space-base-fluid); }
.gap-lg-scaled { gap: var(--space-lg-fluid); }
.gap-xl-scaled { gap: var(--space-xl-fluid); }
.gap-2xl-scaled { gap: var(--space-2xl); }

/* ================== 动态圆角类 ================== */
/* 使用uni.scss中定义的动态圆角变量 */
.rounded-xs-scaled { border-radius: var(--radius-xs); }
.rounded-sm-scaled { border-radius: var(--radius-sm); }
.rounded-base-scaled { border-radius: var(--radius-base); }
.rounded-lg-scaled { border-radius: var(--radius-lg); }
.rounded-xl-scaled { border-radius: var(--radius-xl); }
.rounded-2xl-scaled { border-radius: var(--radius-2xl); }

/* ================== 动态边框类 ================== */
/* 边框宽度通常不需要缩放，保持固定值 */
.border-scaled { border-width: 1rpx; }
.border-2-scaled { border-width: 2rpx; }
.border-4-scaled { border-width: 4rpx; }

/* ================== 动态网格系统 ================== */
/* 保持布局结构不变的网格系统 */
.grid-scaled {
  display: grid;
  gap: var(--space-base-fluid);
}

/* 强制保持网格列数，不因屏幕大小改变 */
.grid-cols-1-scaled { grid-template-columns: 1fr !important; }
.grid-cols-2-scaled { grid-template-columns: repeat(2, 1fr) !important; }
.grid-cols-3-scaled { grid-template-columns: repeat(3, 1fr) !important; }
.grid-cols-4-scaled { grid-template-columns: repeat(4, 1fr) !important; }

/* ================== 动态Flex系统 ================== */
.flex-scaled {
  display: flex;
  gap: var(--space-base-fluid);
}

.flex-col-scaled {
  display: flex;
  flex-direction: column;
  gap: var(--space-base-fluid);
}

/* ================== 动态卡片组件 ================== */
.card-scaled {
  padding: var(--card-padding);
  border-radius: var(--card-radius);
  border: 1rpx solid var(--border-color, #e5e7eb);
  background: var(--bg-card, #ffffff);
  box-shadow: var(--shadow-lg);
}

.card-header-scaled {
  padding: var(--space-base);
  border-bottom: 1rpx solid var(--border-color, #e5e7eb);
  margin-bottom: var(--space-base);
}

.card-body-scaled {
  padding: var(--card-padding);
}

.card-footer-scaled {
  padding: var(--space-base);
  border-top: 1rpx solid var(--border-color, #e5e7eb);
  margin-top: var(--space-base);
}

/* ================== 动态按钮组件 ================== */
.btn-scaled {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--btn-radius);
  font-size: var(--font-base);
  border: 1rpx solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  height: var(--button-height);
}

.btn-sm-scaled {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-sm);
  border-radius: var(--radius-sm);
}

.btn-lg-scaled {
  padding: var(--space-base) var(--space-xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-lg);
}

/* ================== 动态图标 ================== */
.icon-xs-scaled {
  width: var(--icon-xs);
  height: var(--icon-xs);
}

.icon-sm-scaled {
  width: var(--icon-sm);
  height: var(--icon-sm);
}

.icon-base-scaled {
  width: var(--icon-base);
  height: var(--icon-base);
}

.icon-lg-scaled {
  width: var(--icon-lg);
  height: var(--icon-lg);
}

.icon-xl-scaled {
  width: var(--icon-xl);
  height: var(--icon-xl);
}

/* ================== 响应式工具类 ================== */
.w-full-scaled { width: 100%; }
.h-full-scaled { height: 100%; }

.min-h-screen-scaled {
  min-height: 100vh;
}

/* 保持宽高比 */
.aspect-square-scaled {
  aspect-ratio: 1 / 1;
}

.aspect-video-scaled {
  aspect-ratio: 16 / 9;
}

/* ================== 动画和过渡 ================== */
.transition-scaled {
  transition: var(--transition-base);
}

.transition-fast-scaled {
  transition: var(--transition-fast);
}

.transition-slow-scaled {
  transition: var(--transition-slow);
}

/* ================== 布局保持工具类 ================== */
/* 确保布局结构在所有屏幕尺寸下保持一致 */
.layout-preserve {
  /* 使用动态缩放而不是改变布局结构 */
  transform: scale(var(--scale-factor));
  transform-origin: top left;
}

.layout-preserve-center {
  transform: scale(var(--scale-factor));
  transform-origin: center;
}
